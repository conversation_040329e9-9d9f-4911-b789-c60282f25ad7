# Custom
_volumes

# General
!.gitkeep
.tmp
dist
.DS_Store
*.log
*.zip
*.rar
*.tar.gz
*.dmg
*.exe
*.msi

# Miscs & Artifacts
*.o
*.so
*.dylib
# Bundle artifact
*.jsbundle

# ENVs
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.development
.env.stage
.env.production

# Android/IntelliJ
.idea
.gradle
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision
# Android build outputs
*.apk
*.aab
*.ipa

# Xcode
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.xcuserstate
**/ios/.xcode.env.local
# Ruby / CocoaPods
**/ios/Pods/
**/vendor/bundle/

# VS Code & Sublime Text
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets
*.sublime-project
*.sublime-workspace

# RN & Expo
.expo
web-build/
expo-env.d.ts
# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*
# fastlane: It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the screenshots whenever they are needed. For more information about the recommended setup visit: https://docs.fastlane.tools/best-practices/source-control/
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Node & JS & TS & npm & yarn
npm-debug.*
yarn-debug.*
yarn-error.*
*.tsbuildinfo
node_modules
build

# PHP & Composer
**/_plugins
vendor
android/
ios/
